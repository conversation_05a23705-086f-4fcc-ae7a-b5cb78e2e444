{"name": "cluster", "homepage": "https://CLUSTER-DS-Club.github.io/cluster", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "npm run dev", "dev": "vite", "build": "vite build && cp dist/index.html dist/404.html", "lint": "eslint .", "preview": "vite preview", "predeploy": "npm run build", "deploy": "gh-pages -d dist"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "lucide-react": "^0.511.0", "motion": "^12.18.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.6.2", "swiper": "^11.2.8"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "gh-pages": "^6.3.0", "globals": "^16.0.0", "tailwind-scrollbar": "^4.0.2", "tailwindcss": "^4.1.11", "vite": "^6.3.5"}}