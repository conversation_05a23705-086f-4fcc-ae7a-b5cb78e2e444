name: Auto Comment on Issue Close

on:
  issues:
    types: [closed]

permissions:
  issues: write

jobs:
  comment-on-close:
    runs-on: ubuntu-latest

    steps:
      - name: 💬 Add Comment to Closed Issue
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          COMMENT=$(cat <<EOF
          {
            "body": "✅ This issue has been closed. Thank you for your contribution! \n\nIf you have any further questions, ideas, or feedback, feel free to join our community on [Discord](https://discord.gg/6QN83D89vx) and stay connected! 💬"
          }
          EOF
          )

          RESPONSE=$(curl -s -o response.json -w "%{http_code}" \
            -X POST \
            -H "Authorization: token $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments \
            -d "$COMMENT")

          cat response.json

          if [ "$RESPONSE" -ne 201 ]; then
            echo "❌ Failed to add comment on issue close."
            exit 1
          fi