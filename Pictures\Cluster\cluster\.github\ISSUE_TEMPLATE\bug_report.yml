name: "🐞 Bug Report"
description: "Report bugs in the CLUSTER portal, including project or algorithm descriptions."
title: '🐛: '
labels: ["bug 🐛"]
assignees:
  - ''

body:
  - type: input
    id: bug-title
    attributes:
      label: "📝 Bug Title"
      placeholder: "Briefly describe the bug (e.g., 'Broken algorithm description link')"
    validations:
      required: true

  - type: textarea
    id: bug-description
    attributes:
      label: "🔍 Bug Description"
      description: "Provide a clear description of the issue."
      placeholder: "What did you expect to happen? What actually happened? Include steps to reproduce if applicable."
    validations:
      required: true

  - type: textarea
    id: changes
    attributes:
      label: "🛠️ Proposed Fix or Approach"
      description: "How would you go about fixing or improving this?"
      placeholder: "Describe your plan or steps to fix the bug..."
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: "📷 Screenshots (Optional)"
      description: "Attach any screenshots to help visualize the bug."
      placeholder: "Paste image links or write N/A if not applicable."
    validations:
      required: false

  - type: input
    id: full-name
    attributes:
      label: "👤 Full Name"
      placeholder: "Enter your full name."
    validations:
      required: true

  - type: input
    id: participant-role
    attributes:
      label: "🎓 Participant Role"
      placeholder: "E.g., GSSOC, SSOC, JWOC, CLUSTER Contributor"
    validations:
      required: true

  - type: markdown
    attributes:
      value: |
        ✅ Thank you for your report!  
        We'll review this bug and assign it for resolution as soon as possible.  
        Happy contributing! 🚀
