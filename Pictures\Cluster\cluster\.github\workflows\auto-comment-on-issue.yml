name: Auto Comment on Issue Open

on:
  issues:
    types: [opened]

permissions:
  issues: write

jobs:
  comment-on-issue:
    runs-on: ubuntu-latest

    steps:
      - name: 💬 Add Comment to Opened Issue
        run: |
          COMMENT=$(cat <<EOF
          {
            "body": "🙌 Thank you for bringing this issue to our attention! We appreciate your input and will look into it shortly. \n\nFeel free to join our community on [Discord](https://discord.gg/6QN83D89vx) to discuss or collaborate further!"
          }
          EOF
          )

          RESPONSE=$(curl -s -o response.json -w "%{http_code}" \
            -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.issue.number }}/comments \
            -d "$COMMENT")

          cat response.json

          if [ "$RESPONSE" -ne 201 ]; then
            echo "❌ Failed to add comment to the issue."
            exit 1
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}