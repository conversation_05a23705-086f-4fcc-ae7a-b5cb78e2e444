# 🛠️ Contributing to CLUSTER

Welcome to the CLUSTER open-source project! 🎉

We’re excited to have you here and appreciate your interest in contributing. This document will guide you through the contribution process.

---

## 📌 Table of Contents

- [Code of Conduct](#-code-of-conduct)
- [Ways to Contribute](#-ways-to-contribute)
- [Getting Started](#-getting-started)
- [Setting Up Locally](#-setting-up-locally)
- [Creating a Pull Request](#-creating-a-pull-request)
- [Commit Message Guidelines](#-commit-message-guidelines)
- [Style Guide](#-style-guide)
- [Join the Discussion](#-join-the-discussion)

---

## 📜 Code of Conduct

Please read and adhere to our [Code of Conduct](CODE_OF_CONDUCT.md). Respect and inclusivity matter.

---

## 🚀 Ways to Contribute

There are many ways to get involved:

- Report bugs
- Fix issues
- Suggest enhancements
- Improve documentation
- Create new features or pages
- Help with UI/UX or testing

---

## 🔧 Getting Started

### 1. Fork the Repository

Click on the **Fork** button in the top-right corner to make a personal copy of this repository.

### 2. Clone Your Fork

```bash
git clone https://github.com/your-username/cluster.git
cd cluster
````

### 3. Install Dependencies

Ensure you have [Node.js](https://nodejs.org/) installed.

```bash
npm install
```

### 4. Start the Development Server

```bash
npm run dev
```

Navigate to `http://localhost:5173` to view the app in the browser.

---

## ✅ Creating a Pull Request

1. **Create a new branch** for your feature/fix:

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes** and **test locally**.

3. **Commit your changes**:

   ```bash
   git add .
   git commit -m "feat: add new dashboard component"
   ```

4. **Push to your fork**:

   ```bash
   git push origin feature/your-feature-name
   ```

5. **Open a Pull Request** on the main repository with a clear title and description.

---

## 💬 Commit Message Guidelines

Use clear, conventional commits:

* `feat`: new feature
* `fix`: bug fix
* `docs`: changes to documentation
* `style`: formatting, missing semi colons, etc.
* `refactor`: code restructuring without behavior change
* `test`: adding or updating tests
* `chore`: maintenance

Example:

```
feat: add user profile card component
```

---

## 🎨 Style Guide

* Use **consistent indentation** (2 spaces)
* Follow **React** and **TailwindCSS** conventions
* Keep components **modular and reusable**
* Use **semantic HTML** and **accessible elements**

---

## 💡 Tips for Contributors

* Browse the [Issues](https://github.com/CLUSTER-DS-Club/cluster/issues) tab to find beginner-friendly tasks.
* Tag your PR with appropriate labels like `enhancement`, `bug`, `documentation`.
* Add screenshots or screen recordings for UI changes.
* Document your code where necessary.

---

## 🧠 Join the Discussion

* Have ideas or questions? [Open a discussion](https://github.com/CLUSTER-DS-Club/cluster/discussions)
* Join our Discord (link to be added)
* Help others in issues and PRs!

---

Thank you for being awesome! 💙
Happy contributing! 🚀
