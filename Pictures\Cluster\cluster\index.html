<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/jpeg" href="/DS_CLUB_LOGO.jpeg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <base href="/cluster/">
  <title>Cluster DS Club</title>
   <!-- Default SEO  -->
  <meta name="description" content="Official site of Cluster – Data Science Club at VIPS." />
  <meta property="og:title" content="Cluster DS Club" />
  <meta property="og:description" content="Explore ML, research, and student projects." />
  <meta property="og:image" content="/banner.png" />
  <meta name="twitter:card" content="summary_large_image" />
  <meta name="twitter:title" content="Cluster DS Club" />
  <meta name="twitter:description" content="Explore ML, research, and student projects." />

  <script type="text/javascript">
    // Single Page Apps for GitHub Pages
    // MIT License
    // https://github.com/rafgraph/spa-github-pages
    (function(l) {
      if (l.search[1] === '/' ) {
        var decoded = l.search.slice(1).split('&').map(function(s) { 
          return s.replace(/~and~/g, '&')
        }).join('?');
        window.history.replaceState(null, null,
            l.pathname.slice(0, -1) + decoded + l.hash
        );
      }
    }(window.location))
  </script>
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>

</html>