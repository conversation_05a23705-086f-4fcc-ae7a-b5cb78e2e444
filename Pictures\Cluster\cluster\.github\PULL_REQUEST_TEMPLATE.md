# 🚀 Pull Request for CLUSTER Portal

### Related Issue Title  
*Enter the title of the issue your PR addresses.*  
_E.g., Improve dashboard loading speed_

---

### Project Goal  
*Describe the aim or objective of the issue or feature.*  
_This PR improves the dashboard loading time by optimizing queries._

---

### Your Full Name  
*Enter your full name.*

---

### GitHub Username  
*Your GitHub handle.*

---

### Email Address  
*Your email for communication.*

---

### Your Role  
*E.g., GSSOC, SSOC, JWOC, Contributor.*

---

### Issue Number (if any)  
*The issue number this PR closes.*  
_E.g., Closes #45_

---

### Describe Your Changes  
*Explain what you added or modified in this PR.*  
_Fixed the user login bug by updating the auth flow._

---

### Type of Change  
*Select one or more:*  
- [ ] Bug fix (non-breaking change)  
- [ ] New feature (non-breaking change)  
- [ ] Code style / formatting  
- [ ] Breaking change (may cause backward incompatibility)  
- [ ] Documentation update  

---

### Testing Details  
*Describe how you tested your changes.*  
_Ran unit tests locally and verified login functionality._

---

### Checklist  
*Please check all that apply:*  
- [ ] I have followed the project's contributing guidelines.  
- [ ] I have self-reviewed my code.  
- [ ] I have added comments where necessary.  
- [ ] I have updated documentation if needed.  
- [ ] My changes do not generate new warnings.  
- [ ] I have added tests or screenshots proving my fix/feature works.  
- [ ] All dependent changes have been merged.  
