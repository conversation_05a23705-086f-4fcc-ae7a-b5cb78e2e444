@import "tailwindcss";

body {
  background: linear-gradient(120deg, #0a1836 0%, #1a2950 100%) !important;
  min-height: 100vh;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  min-height: 100vh;
  background: none !important;
}

.no-hover-effect:hover {
  transform: none !important;
  transition: none !important;
}

/* app.css */
.sidebar-scrollbar::-webkit-scrollbar {
  width: 8px;
}
.sidebar-scrollbar::-webkit-scrollbar-thumb {
  background: #06b6d4; /* cyan-500 */
  border-radius: 8px;
}
.sidebar-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}
.sidebar-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #06b6d4 transparent;
}
