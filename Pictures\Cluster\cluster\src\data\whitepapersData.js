const whitepapersData = [
  {
    id: 1,
    title: "Optimizing React Performance in Large-Scale Applications",
    abstract: "A comprehensive deep dive into React memoization, lazy loading, virtual DOM tuning, and advanced optimization techniques for enterprise applications.",
    author: "<PERSON><PERSON><PERSON><PERSON>.",
    date: "2025-06-15",
    tags: ["React", "Frontend", "Performance"],
    url: "#"
  },
  {
    id: 2,
    title: "Machine Learning Model Deployment at Scale",
    abstract: "Best practices for deploying ML models in production environments, covering containerization, monitoring, and automated retraining pipelines.",
    author: "<PERSON><PERSON>",
    date: "2025-05-28",
    tags: ["ML", "DevOps", "AI"],
    url: "#"
  },
  {
    id: 3,
    title: "Blockchain Security Patterns and Anti-Patterns",
    abstract: "An analysis of common security vulnerabilities in smart contracts and decentralized applications, with practical mitigation strategies.",
    author: "<PERSON><PERSON>",
    date: "2025-05-10",
    tags: ["Blockchain", "Security", "Web3"],
    url: "#"
  },
  {
    id: 4,
    title: "Microservices Architecture: Design Patterns for Data Consistency",
    abstract: "Exploring SAGA patterns, event sourcing, and CQRS for maintaining data consistency across distributed microservices architectures.",
    author: "<PERSON><PERSON>",
    date: "2025-04-22",
    tags: ["Microservices", "Architecture", "Backend"],
    url: "#"
  },
  {
    id: 5,
    title: "Advanced CSS Grid and Flexbox Techniques",
    abstract: "Modern layout techniques using CSS Grid and Flexbox for creating responsive, accessible, and maintainable web interfaces.",
    author: "Vikram Patel",
    date: "2025-04-08",
    tags: ["CSS", "Frontend", "Design"],
    url: "#"
  },
  {
    id: 6,
    title: "Natural Language Processing for Code Analysis",
    abstract: "Leveraging transformer models and attention mechanisms to analyze code quality, detect bugs, and suggest improvements automatically.",
    author: "Meera Reddy",
    date: "2025-03-25",
    tags: ["NLP", "AI", "Code Analysis"],
    url: "#"
  },
  {
    id: 7,
    title: "Cloud-Native Application Security Framework",
    abstract: "A comprehensive security framework for cloud-native applications covering container security, service mesh, and zero-trust architecture.",
    author: "Arjun Gupta",
    date: "2025-03-12",
    tags: ["Cloud", "Security", "DevOps"],
    url: "#"
  },
  {
    id: 8,
    title: "Real-time Data Processing with Apache Kafka",
    abstract: "Building scalable real-time data pipelines using Apache Kafka, including stream processing patterns and monitoring strategies.",
    author: "Sneha Joshi",
    date: "2025-02-28",
    tags: ["Data Engineering", "Kafka", "Streaming"],
    url: "#"
  },
  {
    id: 9,
    title: "GraphQL Federation in Distributed Systems",
    abstract: "Implementing GraphQL federation to create unified APIs across microservices while maintaining team autonomy and service boundaries.",
    author: "Karthik Menon",
    date: "2025-02-14",
    tags: ["GraphQL", "API", "Microservices"],
    url: "#"
  }
];

export default whitepapersData;