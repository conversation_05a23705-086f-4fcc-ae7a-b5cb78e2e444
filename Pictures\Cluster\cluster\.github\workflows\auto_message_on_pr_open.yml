name: Auto Comment on PR Open

on:
  pull_request_target:
    types: [opened]

permissions:
  pull-requests: write
  issues: write

jobs:
  comment-and-assign:
    runs-on: ubuntu-latest

    steps:
      - name: 💬 Comment on PR Open
        run: |
          COMMENT=$(cat <<EOF
          {
            "body": "👋 Thank you for opening this pull request! We're excited to review your contribution. Please give us a moment, and we'll get back to you shortly. 🙌\n\nFeel free to join our community on [Discord](https://discord.gg/6QN83D89vx) to chat and collaborate!"
          }
          EOF
          )
          
          RESPONSE=$(curl -s -o response.json -w "%{http_code}" \
            -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/issues/${{ github.event.pull_request.number }}/comments \
            -d "$COMMENT")
          
          cat response.json
          
          if [ "$RESPONSE" -ne 201 ]; then
            echo "❌ Failed to add comment to the PR."
            exit 1
          fi

      - name: 👥 Add Reviewer to PR
        run: |
          REVIEWERS=$(cat <<EOF
          {
            "reviewers": ["UTSAVS26"]
          }
          EOF
          )
          
          RESPONSE=$(curl -s -o response.json -w "%{http_code}" \
            -X POST \
            -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}/requested_reviewers \
            -d "$REVIEWERS")
          
          cat response.json
          
          if [ "$RESPONSE" -ne 201 ]; then
            echo "❌ Failed to add reviewer."
            exit 1
          fi

        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}